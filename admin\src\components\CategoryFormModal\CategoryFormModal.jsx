import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiX } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { closeCategoryForm } from '../../redux/slices/categoriesSlice';
import { createCategory, updateCategory } from '../../redux/slices/categoriesSlice';

const CategoryFormModal = () => {
  const dispatch = useDispatch();
  const { categoryForm, loading } = useSelector((state) => state.categories);
  const { isOpen, mode, data } = categoryForm;

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: '',
    color: '#f8f9fa',
    order: 0,
    isActive: true,
  });

  useEffect(() => {
    if (mode === 'edit' && data) {
      setFormData({
        name: data.name || '',
        description: data.description || '',
        icon: data.icon || '',
        color: data.color || '#f8f9fa',
        order: data.order || 0,
        isActive: data.isActive !== undefined ? data.isActive : true,
      });
    } else {
      // Reset form for create mode
      setFormData({
        name: '',
        description: '',
        icon: '',
        color: '#f8f9fa',
        order: 0,
        isActive: true,
      });
    }
  }, [mode, data]);

  const predefinedIcons = [
    '🏠', '🏥', '🏨', '🎓', '✂️', '🚶', '🐕', '🐱', '🦴', '🎾',
    '🧸', '🍖', '💊', '🩺', '🛁', '🚗', '📱', '💻', '📋', '⭐'
  ];

  const predefinedColors = [
    '#f8f9fa', '#e8f5e8', '#e8f0ff', '#ffe8e8', '#fff8e8', 
    '#f8e8ff', '#e8fff8', '#ffe8f8', '#f0f8ff', '#fff5ee'
  ];

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name || !formData.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const categoryData = {
        ...formData,
        order: parseInt(formData.order) || 0,
      };

      if (mode === 'edit') {
        await dispatch(updateCategory({ id: data._id, data: categoryData })).unwrap();
        toast.success('Category updated successfully');
      } else {
        await dispatch(createCategory(categoryData)).unwrap();
        toast.success('Category created successfully');
      }
      
      dispatch(closeCategoryForm());
    } catch (error) {
      toast.error(error || 'Failed to save category');
    }
  };

  const handleClose = () => {
    dispatch(closeCategoryForm());
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={handleClose}
        />

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {mode === 'edit' ? 'Edit Category' : 'Create New Category'}
              </h3>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <FiX className="w-6 h-6" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Name */}
              <div>
                <label className="form-label">Name *</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="form-input"
                  placeholder="e.g., Pet Sitting"
                  required
                />
              </div>

              {/* Description */}
              <div>
                <label className="form-label">Description *</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  className="form-textarea"
                  rows={3}
                  placeholder="Brief description of this category"
                  required
                />
              </div>

              {/* Icon Selection */}
              <div>
                <label className="form-label">Icon</label>
                <div className="space-y-2">
                  <input
                    type="text"
                    name="icon"
                    value={formData.icon}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="Enter emoji or icon"
                  />
                  <div className="grid grid-cols-10 gap-2">
                    {predefinedIcons.map((icon, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, icon }))}
                        className={`p-2 text-lg border rounded hover:bg-gray-100 ${
                          formData.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                        }`}
                      >
                        {icon}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Color Selection */}
              <div>
                <label className="form-label">Background Color</label>
                <div className="space-y-2">
                  <input
                    type="color"
                    name="color"
                    value={formData.color}
                    onChange={handleChange}
                    className="w-full h-10 border border-gray-300 rounded cursor-pointer"
                  />
                  <div className="grid grid-cols-5 gap-2">
                    {predefinedColors.map((color, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, color }))}
                        className={`w-full h-8 border rounded ${
                          formData.color === color ? 'border-blue-500 border-2' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Order and Status */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="form-label">Display Order</label>
                  <input
                    type="number"
                    name="order"
                    value={formData.order}
                    onChange={handleChange}
                    className="form-input"
                    min="0"
                    placeholder="0"
                  />
                </div>
                <div className="flex items-center">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleChange}
                      className="mr-2"
                    />
                    Active Category
                  </label>
                </div>
              </div>

              {/* Preview */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="text-sm font-medium text-gray-700 mb-2">Preview</div>
                <div
                  className="p-3 rounded-lg border"
                  style={{ backgroundColor: formData.color }}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-xl">{formData.icon || '📁'}</span>
                    <div>
                      <div className="font-medium text-gray-900">
                        {formData.name || 'Category Name'}
                      </div>
                      <div className="text-sm text-gray-600">
                        {formData.description || 'Category description'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="spinner mr-2"></div>
                  {mode === 'edit' ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                mode === 'edit' ? 'Update Category' : 'Create Category'
              )}
            </button>
            <button
              onClick={handleClose}
              disabled={loading}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryFormModal;

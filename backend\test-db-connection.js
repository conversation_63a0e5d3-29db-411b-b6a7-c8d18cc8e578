const mongoose = require('mongoose');
const dotenv = require('dotenv');

dotenv.config();

const testConnection = async () => {
  try {
    console.log('🔄 Testing MongoDB connection...');
    console.log('📍 MongoDB URI exists:', !!process.env.MONGO_URI);
    console.log('📍 MongoDB URI preview:', process.env.MONGO_URI ? process.env.MONGO_URI.substring(0, 20) + '...' : 'Not found');
    
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ MongoDB connection successful!');
    console.log(`📍 Connected to: ${conn.connection.host}`);
    console.log(`📊 Database: ${conn.connection.name}`);
    console.log(`🔗 Connection state: ${mongoose.connection.readyState}`);
    
    // Test a simple operation
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`📋 Collections found: ${collections.length}`);
    collections.forEach(col => console.log(`  - ${col.name}`));
    
    // Close connection
    await mongoose.connection.close();
    console.log('🔌 Connection closed successfully');
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:');
    console.error('📝 Error message:', error.message);
    console.error('🔍 Error code:', error.code);
    console.error('🏷️ Error name:', error.name);
    
    if (error.name === 'MongoNetworkError') {
      console.log('💡 This is a network error. Check:');
      console.log('   - Internet connection');
      console.log('   - MongoDB Atlas cluster status');
      console.log('   - IP whitelist in MongoDB Atlas');
    }
    
    if (error.name === 'MongoParseError') {
      console.log('💡 This is a connection string error. Check:');
      console.log('   - MONGO_URI format in .env file');
      console.log('   - Username and password in connection string');
    }
    
    process.exit(1);
  }
};

testConnection();

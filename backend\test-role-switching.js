const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5000/api';
let userToken = '';
let userId = '';

// Test user credentials
const testUser = {
  name: 'Role Switch Test User',
  email: '<EMAIL>',
  password: 'password123',
  userType: 'Pet Owner',
  phoneNumber: '+1234567890'
};

// Helper function for API calls
const apiCall = async (method, endpoint, data = null, token = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ API Error (${method} ${endpoint}):`, error.response?.data || error.message);
    throw error;
  }
};

// Test functions
async function registerAndLoginUser() {
  console.log('👤 Registering test user...');
  try {
    await apiCall('POST', '/auth/signup', testUser);
    console.log('✅ User registered successfully');
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('ℹ️  User already exists, continuing...');
    } else {
      throw error;
    }
  }
  
  console.log('🔐 Logging in as user...');
  const response = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password
  });
  userToken = response.token;
  userId = response.user._id;
  
  console.log('✅ User login successful');
  console.log(`📋 User ID: ${userId}`);
  console.log(`📋 Current Role: ${response.user.currentRole}`);
  console.log(`📋 Available Roles: ${response.user.availableRoles?.join(', ') || 'None'}`);
  console.log(`📋 Can Switch Roles: ${response.user.canSwitchRoles}`);
  
  return response.user;
}

async function getRoleInfo() {
  console.log('📊 Getting current role information...');
  const response = await apiCall('GET', '/auth/role-info', null, userToken);
  
  console.log('✅ Role information retrieved');
  console.log(`📋 User Type: ${response.roleInfo.userType}`);
  console.log(`📋 Current Role: ${response.roleInfo.currentRole}`);
  console.log(`📋 Available Roles: ${response.roleInfo.availableRoles.join(', ')}`);
  console.log(`📋 Can Switch Roles: ${response.roleInfo.canSwitchRoles}`);
  console.log(`📋 Recent Role History: ${response.roleInfo.roleHistory.length} switches`);
  
  return response.roleInfo;
}

async function enableRoleSwitching() {
  console.log('🔧 Enabling role switching for test user...');
  
  // First, login as admin to enable role switching
  const adminResponse = await apiCall('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'password'
  });
  
  const adminToken = adminResponse.token;
  console.log('✅ Admin login successful');
  
  // Enable role switching for the test user
  const response = await apiCall('POST', '/auth/enable-role-switching', {
    userId: userId,
    rolesToEnable: ['Pet Owner', 'Business']
  }, adminToken);
  
  console.log('✅ Role switching enabled successfully');
  console.log(`📋 Available Roles: ${response.user.availableRoles.join(', ')}`);
  
  return response.user;
}

async function testRoleSwitching() {
  console.log('🔄 Testing role switching...');
  
  // Get current role info
  const roleInfo = await getRoleInfo();
  const currentRole = roleInfo.currentRole;
  const availableRoles = roleInfo.availableRoles;
  
  if (availableRoles.length === 0) {
    console.log('⚠️  No roles available for switching. Enabling role switching first...');
    await enableRoleSwitching();
    
    // Refresh role info after enabling
    const updatedRoleInfo = await getRoleInfo();
    availableRoles.push(...updatedRoleInfo.availableRoles);
  }
  
  if (availableRoles.length === 0) {
    console.log('❌ No roles available for switching after enabling');
    return;
  }
  
  // Switch to the first available role
  const targetRole = availableRoles[0];
  console.log(`🔄 Switching from ${currentRole} to ${targetRole}...`);
  
  const switchResponse = await apiCall('POST', '/auth/switch-role', {
    newRole: targetRole
  }, userToken);
  
  // Update token with new role information
  userToken = switchResponse.token;
  
  console.log('✅ Role switch successful');
  console.log(`📋 Previous Role: ${switchResponse.previousRole}`);
  console.log(`📋 New Current Role: ${switchResponse.user.currentRole}`);
  console.log(`📋 Available Roles: ${switchResponse.user.availableRoles.join(', ')}`);
  
  return switchResponse.user;
}

async function testRoleBasedAccess() {
  console.log('🔐 Testing role-based access...');
  
  // Get current role
  const roleInfo = await getRoleInfo();
  const currentRole = roleInfo.currentRole;
  
  console.log(`📋 Testing access with current role: ${currentRole}`);
  
  // Test Pet Owner specific endpoints
  if (currentRole === 'Pet Owner') {
    console.log('🐕 Testing Pet Owner access...');
    
    try {
      const petsResponse = await apiCall('GET', '/pet/all', null, userToken);
      console.log(`✅ Pet Owner access successful - Found ${petsResponse.pets?.length || 0} pets`);
    } catch (error) {
      console.log('❌ Pet Owner access failed:', error.response?.data?.message);
    }
    
    try {
      const appointmentsResponse = await apiCall('GET', '/appointment/customer', null, userToken);
      console.log(`✅ Customer appointments access successful - Found ${appointmentsResponse.appointments?.length || 0} appointments`);
    } catch (error) {
      console.log('❌ Customer appointments access failed:', error.response?.data?.message);
    }
  }
  
  // Test Business specific endpoints
  if (currentRole === 'Business') {
    console.log('🏢 Testing Business access...');
    
    try {
      const servicesResponse = await apiCall('GET', '/service/business/all', null, userToken);
      console.log(`✅ Business services access successful - Found ${servicesResponse.services?.length || 0} services`);
    } catch (error) {
      console.log('❌ Business services access failed:', error.response?.data?.message);
    }
    
    try {
      const appointmentsResponse = await apiCall('GET', '/appointment/business', null, userToken);
      console.log(`✅ Business appointments access successful - Found ${appointmentsResponse.appointments?.length || 0} appointments`);
    } catch (error) {
      console.log('❌ Business appointments access failed:', error.response?.data?.message);
    }
  }
}

async function testMultipleRoleSwitches() {
  console.log('🔄 Testing multiple role switches...');
  
  const roleInfo = await getRoleInfo();
  const availableRoles = roleInfo.availableRoles;
  
  if (availableRoles.length < 1) {
    console.log('⚠️  Not enough roles available for multiple switches');
    return;
  }
  
  // Switch between roles multiple times
  for (let i = 0; i < Math.min(3, availableRoles.length); i++) {
    const targetRole = availableRoles[i % availableRoles.length];
    
    console.log(`🔄 Switch ${i + 1}: Switching to ${targetRole}...`);
    
    const switchResponse = await apiCall('POST', '/auth/switch-role', {
      newRole: targetRole
    }, userToken);
    
    userToken = switchResponse.token;
    
    console.log(`✅ Switch ${i + 1} successful - Now in ${switchResponse.user.currentRole} role`);
    
    // Test access after each switch
    await testRoleBasedAccess();
    
    // Small delay between switches
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

async function testInvalidRoleSwitch() {
  console.log('❌ Testing invalid role switch...');
  
  try {
    await apiCall('POST', '/auth/switch-role', {
      newRole: 'Admin'
    }, userToken);
    
    console.log('❌ Invalid role switch should have failed but succeeded');
  } catch (error) {
    console.log('✅ Invalid role switch correctly rejected:', error.response?.data?.message);
  }
  
  try {
    await apiCall('POST', '/auth/switch-role', {
      newRole: 'InvalidRole'
    }, userToken);
    
    console.log('❌ Invalid role switch should have failed but succeeded');
  } catch (error) {
    console.log('✅ Invalid role switch correctly rejected:', error.response?.data?.message);
  }
}

async function testProfileAccess() {
  console.log('👤 Testing profile access with role switching...');

  const profileResponse = await apiCall('GET', '/profile/get-profile', null, userToken);

  console.log('✅ Profile access successful');
  console.log(`📋 Profile Name: ${profileResponse.profile.name}`);
  console.log(`📋 Profile User Type: ${profileResponse.profile.userType}`);
  console.log(`📋 Profile Current Role: ${profileResponse.profile.currentRole}`);
  console.log(`📋 Profile Available Roles: ${profileResponse.profile.availableRoles?.join(', ')}`);
  console.log(`📋 Profile Can Switch Roles: ${profileResponse.profile.canSwitchRoles}`);
}

async function testSharedDataAccess() {
  console.log('📊 Testing shared data access across roles...');

  const sharedDataResponse = await apiCall('GET', '/profile/shared-data', null, userToken);

  console.log('✅ Shared data access successful');
  console.log(`📋 Current Role: ${sharedDataResponse.data.currentRole}`);
  console.log(`📋 Available Roles: ${sharedDataResponse.data.availableRoles?.join(', ')}`);

  if (sharedDataResponse.data.petOwnerData) {
    console.log(`📋 Pet Owner Data: ${sharedDataResponse.data.petOwnerData.totalPets} pets, ${sharedDataResponse.data.petOwnerData.totalAppointments} appointments`);
  }

  if (sharedDataResponse.data.businessData) {
    console.log(`📋 Business Data: ${sharedDataResponse.data.businessData.totalServices} services, ${sharedDataResponse.data.businessData.totalAppointments} appointments`);
  }
}

// Main test function
async function runRoleSwitchingTests() {
  try {
    console.log('🚀 Starting Role Switching Tests\n');
    
    // Step 1: Register and login user
    await registerAndLoginUser();
    console.log('');
    
    // Step 2: Get initial role information
    await getRoleInfo();
    console.log('');
    
    // Step 3: Enable role switching
    await enableRoleSwitching();
    console.log('');
    
    // Step 4: Test basic role switching
    await testRoleSwitching();
    console.log('');
    
    // Step 5: Test role-based access
    await testRoleBasedAccess();
    console.log('');
    
    // Step 6: Test multiple role switches
    await testMultipleRoleSwitches();
    console.log('');
    
    // Step 7: Test invalid role switches
    await testInvalidRoleSwitch();
    console.log('');
    
    // Step 8: Test profile access
    await testProfileAccess();
    console.log('');

    // Step 9: Test shared data access
    await testSharedDataAccess();
    console.log('');

    // Step 10: Final role information
    await getRoleInfo();
    console.log('');
    
    console.log('🎉 All Role Switching Tests Completed Successfully!');
    console.log('📚 The role switching system is fully functional with:');
    console.log('   ✅ User role switching without re-login');
    console.log('   ✅ Role-based access control');
    console.log('   ✅ JWT token updates with role changes');
    console.log('   ✅ Profile integration with role information');
    console.log('   ✅ Admin role management');
    console.log('   ✅ Invalid role switch protection');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run tests
runRoleSwitchingTests();

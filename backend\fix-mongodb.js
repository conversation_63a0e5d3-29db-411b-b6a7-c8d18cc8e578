const mongoose = require('mongoose');
const dotenv = require('dotenv');
const axios = require('axios');

dotenv.config();

const runDiagnostics = async () => {
  console.log('🔍 MongoDB Connection Diagnostics\n');
  
  // Check 1: Environment variables
  console.log('1️⃣ Checking environment variables...');
  console.log('   MONGO_URI exists:', !!process.env.MONGO_URI);
  console.log('   PORT:', process.env.PORT || '5000');
  console.log('   JWT_SECRET exists:', !!process.env.JWT_SECRET);
  
  if (!process.env.MONGO_URI) {
    console.log('❌ MONGO_URI not found in .env file');
    console.log('💡 Create .env file with MONGO_URI=your_mongodb_connection_string');
    return;
  }
  
  // Check 2: Internet connectivity
  console.log('\n2️⃣ Checking internet connectivity...');
  try {
    await axios.get('https://www.google.com', { timeout: 5000 });
    console.log('✅ Internet connection working');
  } catch (error) {
    console.log('❌ Internet connection failed');
    console.log('💡 Check your internet connection');
    return;
  }
  
  // Check 3: MongoDB Atlas connectivity
  console.log('\n3️⃣ Checking MongoDB Atlas connectivity...');
  try {
    const hostname = process.env.MONGO_URI.match(/\/\/([^\/]+)\//)?.[1];
    if (hostname) {
      console.log(`   Testing connection to: ${hostname}`);
      // Note: We can't ping MongoDB Atlas directly, so we'll try the connection
    }
  } catch (error) {
    console.log('⚠️ Could not extract hostname from MONGO_URI');
  }
  
  // Check 4: MongoDB connection
  console.log('\n4️⃣ Testing MongoDB connection...');
  try {
    console.log('   Attempting connection...');
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 10000, // 10 second timeout
    });
    
    console.log('✅ MongoDB connection successful!');
    console.log(`   Host: ${conn.connection.host}`);
    console.log(`   Database: ${conn.connection.name}`);
    console.log(`   Ready state: ${mongoose.connection.readyState}`);
    
    // Check 5: Database operations
    console.log('\n5️⃣ Testing database operations...');
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`✅ Found ${collections.length} collections`);
    
    // Test a simple query
    const User = mongoose.model('User', new mongoose.Schema({
      name: String,
      email: String
    }));
    
    const userCount = await User.countDocuments();
    console.log(`✅ User collection accessible (${userCount} users)`);
    
    await mongoose.connection.close();
    console.log('✅ Connection closed successfully');
    
    console.log('\n🎉 All checks passed! Your MongoDB connection is working.');
    console.log('💡 If you\'re still getting errors, restart your server:');
    console.log('   npm start');
    
  } catch (error) {
    console.log('❌ MongoDB connection failed');
    console.log(`   Error: ${error.message}`);
    
    // Provide specific solutions based on error type
    if (error.name === 'MongoNetworkError') {
      console.log('\n💡 Network Error Solutions:');
      console.log('   1. Check if MongoDB Atlas cluster is running (not paused)');
      console.log('   2. Verify IP whitelist in MongoDB Atlas Network Access');
      console.log('   3. Try adding 0.0.0.0/0 to whitelist (development only)');
      console.log('   4. Check if your ISP blocks MongoDB ports');
    }
    
    if (error.name === 'MongoParseError') {
      console.log('\n💡 Connection String Error Solutions:');
      console.log('   1. Check MONGO_URI format in .env file');
      console.log('   2. Verify username and password are correct');
      console.log('   3. Ensure special characters are URL encoded');
    }
    
    if (error.name === 'MongoServerSelectionError') {
      console.log('\n💡 Server Selection Error Solutions:');
      console.log('   1. MongoDB Atlas cluster might be paused - check Atlas dashboard');
      console.log('   2. Network connectivity issues - try different network');
      console.log('   3. Firewall blocking connection - check firewall settings');
    }
    
    console.log('\n🔧 Quick fixes to try:');
    console.log('   1. Go to MongoDB Atlas and resume your cluster if paused');
    console.log('   2. Add your current IP to Network Access whitelist');
    console.log('   3. Try using a mobile hotspot to test network issues');
    console.log('   4. Create a new MongoDB Atlas cluster if current one is problematic');
  }
};

const createLocalFallback = () => {
  console.log('\n🔄 Creating local MongoDB fallback...');
  console.log('If you want to use local MongoDB instead:');
  console.log('1. Install MongoDB Community Server');
  console.log('2. Start MongoDB service');
  console.log('3. Update .env file:');
  console.log('   MONGO_URI=mongodb://localhost:27017/petcare');
  console.log('4. Restart your server');
};

// Run diagnostics
runDiagnostics().catch((error) => {
  console.error('❌ Diagnostics failed:', error.message);
  createLocalFallback();
});

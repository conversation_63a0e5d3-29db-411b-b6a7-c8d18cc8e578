import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  FiPlus, 
  FiEdit, 
  FiTrash2,
  FiRefreshCw,
  FiSettings,
  FiEye,
  FiEyeOff
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { 
  fetchCategories, 
  openCategoryForm, 
  deleteCategory,
  seedCategories
} from '../../redux/slices/categoriesSlice';
import CategoryFormModal from '../../components/CategoryFormModal/CategoryFormModal';
import { openDeleteConfirm } from '../../redux/slices/uiSlice';

const Categories = () => {
  const dispatch = useDispatch();
  const { categories, loading } = useSelector((state) => state.categories);
  const [filter, setFilter] = useState('all'); // 'all', 'active', 'inactive'

  useEffect(() => {
    dispatch(fetchCategories());
  }, [dispatch]);

  const handleRefresh = () => {
    dispatch(fetchCategories());
  };

  const handleCreateCategory = () => {
    dispatch(openCategoryForm({ mode: 'create' }));
  };

  const handleEditCategory = (category) => {
    dispatch(openCategoryForm({ mode: 'edit', data: category }));
  };

  const handleDeleteCategory = (category) => {
    dispatch(openDeleteConfirm({
      title: 'Delete Category',
      message: `Are you sure you want to delete "${category.name}"? This action cannot be undone.`,
      onConfirm: async () => {
        try {
          await dispatch(deleteCategory(category._id)).unwrap();
          toast.success('Category deleted successfully');
        } catch (error) {
          toast.error(error || 'Failed to delete category');
        }
      }
    }));
  };

  const handleSeedCategories = async () => {
    try {
      await dispatch(seedCategories()).unwrap();
      toast.success('Default categories created successfully');
      dispatch(fetchCategories());
    } catch (error) {
      toast.error(error || 'Failed to seed categories');
    }
  };

  const filteredCategories = categories.filter(category => {
    if (filter === 'active') return category.isActive;
    if (filter === 'inactive') return !category.isActive;
    return true;
  });

  const getStatusBadge = (isActive) => {
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
        isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Categories</h1>
          <p className="text-gray-600">Manage service categories for your platform</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleSeedCategories}
            disabled={loading}
            className="btn btn-secondary"
          >
            <FiSettings className="w-4 h-4" />
            Seed Default
          </button>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="btn btn-secondary"
          >
            <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={handleCreateCategory}
            className="btn btn-primary"
          >
            <FiPlus className="w-4 h-4" />
            Create Category
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="card">
          <div className="card-body text-center">
            <div className="text-2xl font-bold text-blue-600">
              {categories.length}
            </div>
            <div className="text-sm text-gray-600">Total Categories</div>
          </div>
        </div>
        <div className="card">
          <div className="card-body text-center">
            <div className="text-2xl font-bold text-green-600">
              {categories.filter(c => c.isActive).length}
            </div>
            <div className="text-sm text-gray-600">Active Categories</div>
          </div>
        </div>
        <div className="card">
          <div className="card-body text-center">
            <div className="text-2xl font-bold text-red-600">
              {categories.filter(c => !c.isActive).length}
            </div>
            <div className="text-sm text-gray-600">Inactive Categories</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Filter:</span>
            <div className="flex space-x-2">
              {[
                { key: 'all', label: 'All Categories' },
                { key: 'active', label: 'Active Only' },
                { key: 'inactive', label: 'Inactive Only' }
              ].map(({ key, label }) => (
                <button
                  key={key}
                  onClick={() => setFilter(key)}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    filter === key
                      ? 'bg-blue-100 text-blue-700 border border-blue-300'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Categories Grid */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">
            Categories ({filteredCategories.length})
          </h3>
        </div>
        <div className="card-body">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="spinner mr-3"></div>
              <span className="text-gray-600">Loading categories...</span>
            </div>
          ) : filteredCategories.length === 0 ? (
            <div className="text-center py-12">
              <FiSettings className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600 mb-4">
                {filter === 'all' ? 'No categories found' : `No ${filter} categories found`}
              </p>
              {categories.length === 0 && (
                <button
                  onClick={handleSeedCategories}
                  className="btn btn-primary"
                >
                  Create Default Categories
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCategories.map((category) => (
                <div
                  key={category._id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  style={{ backgroundColor: category.color || '#f8f9fa' }}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">
                        {category.icon || '📁'}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{category.name}</h4>
                        {getStatusBadge(category.isActive)}
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleEditCategory(category)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="Edit Category"
                      >
                        <FiEdit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCategory(category)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete Category"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">
                    {category.description}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Order: {category.order || 0}</span>
                    <div className="flex items-center space-x-1">
                      {category.isActive ? (
                        <FiEye className="w-3 h-3" />
                      ) : (
                        <FiEyeOff className="w-3 h-3" />
                      )}
                      <span>{category.isActive ? 'Visible' : 'Hidden'}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Category Form Modal */}
      <CategoryFormModal />
    </div>
  );
};

export default Categories;

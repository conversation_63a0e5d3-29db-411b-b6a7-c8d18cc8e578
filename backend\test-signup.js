const axios = require('axios');

const testSignup = async () => {
  try {
    console.log('Testing signup API...');
    
    const response = await axios.post('http://localhost:5000/api/auth/signup', {
      name: 'Test Business User',
      email: '<EMAIL>',
      password: 'password123',
      userType: 'Business'
    });
    
    console.log('✅ Signup successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('❌ Signup failed:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
};

const testLogin = async () => {
  try {
    console.log('\nTesting login API...');
    
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('✅ Login successful!');
    console.log('User:', response.data.user);
    console.log('Token received:', !!response.data.token);
    
  } catch (error) {
    console.log('❌ Login failed:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }
};

const runTests = async () => {
  await testSignup();
  await testLogin();
};

runTests();
